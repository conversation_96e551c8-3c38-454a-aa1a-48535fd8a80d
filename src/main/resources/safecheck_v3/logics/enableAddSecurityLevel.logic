sql.execSQL("v3_safecheck_sql", "
    update tu
     set f_security_level_num=f_security_level_num-1,f_security_valid_date = DATEADD(MONTH, 3, GETDATE())
	 from t_userinfo tu left join t_userfiles tu2 on tu.f_userinfo_id=tu2.f_userinfo_id
     where f_security_valid_date <= GETDATE()  and tu2.f_user_type = '民用' and f_security_level_num > 1
"),
sql.execSQL("v3_safecheck_sql", "
    update tu
     set f_security_level_num=f_security_level_num-1,f_security_valid_date = DATEADD(MONTH, 6, GETDATE())
     from t_userinfo tu left join t_userfiles tu2 on tu.f_userinfo_id=tu2.f_userinfo_id
     where f_security_valid_date <= GETDATE()  and tu2.f_user_type = '非民用' and f_security_level_num > 1
")
