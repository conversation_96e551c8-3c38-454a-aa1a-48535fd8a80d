log.info("进入GetDefectPaperWithDefectLevel.logic ===> {data}"),
res = sql.querySQL("v3_safecheck_sql", "
	SELECT
		STUFF((
			SELECT ',[' + f_defect_level + ']'
				FROM (
					SELECT
						CASE
							WHEN f_defect_level = '' THEN '其他隐患'
							WHEN f_defect_level IS NULL THEN '其他隐患'
							ELSE f_defect_level
						END
						f_defect_level
					FROM
						t_devices_items
				) t_
			GROUP BY f_defect_level FOR XML PATH('')
		),1,1,'') defect_list
"),
log.info("{res}"),
querySql = "
		SELECT
			f_paper_id,{res[0].defect_list}
		FROM (
			SELECT
				pd.f_paper_id,
				CASE
					WHEN di.f_defect_level = '' THEN '其他隐患'
					WHEN di.f_defect_level IS NULL THEN '其他隐患'
					ELSE di.f_defect_level
				END
					f_defect_level,
				(
					SELECT
						CASE di.f_is_defect
							WHEN 'true' THEN 1
							ELSE 0
						END defect_count
					FROM
						t_devices_items
					WHERE
						id = di.id
				) defect_count
			FROM
				t_check_paper cp
				LEFT JOIN t_paper_devices pd ON pd.f_paper_id = cp.id
				LEFT JOIN t_devices_items di ON di.f_device_id = pd.id
		) t_
		PIVOT (SUM(defect_count) FOR f_defect_level IN ({res[0].defect_list}))t_
",
log.info("{querySql}"),
querySql = "
	SELECT
		*,
		(
			CASE
				WHEN f_defect_count > 0
					THEN '有隐患'
				ELSE '无隐患'
			END
		) f_has_defect
	FROM (
		SELECT
			cp.id,
			cp.f_entry_status,
			cp.f_userinfo_code,
			cp.f_user_name,
			cp.f_onsite_time,
			cp.f_offsite_time,
			cp.f_checker_name,
			ua.f_address,
			cp.f_check_plan_id,
			cpl.f_plan_name,
			ui.f_userinfo_id,
			ui.f_filialeid,
			(
				SELECT
					SUM(
						CASE di.f_is_defect
							WHEN 'true'
								THEN 1
							ELSE 0
						END
					) f_defect_count
				FROM
					t_paper_devices pd
					LEFT JOIN t_devices_items di ON di.f_device_id = pd.id
				WHERE
					pd.f_paper_id = cp.id
			)
			f_defect_count
		FROM
			t_check_paper cp
			LEFT JOIN t_check_plan cpl ON cpl.id = cp.f_check_plan_id
			LEFT JOIN t_userinfo ui ON ui.f_userinfo_id = cp.f_userinfoid
			LEFT JOIN t_user_address ua ON ua.f_userinfo_id = cp.f_userinfoid
		WHERE
			ui.f_user_state  = '正常'
			AND ui.f_filialeid in ({data.f_filialeids})
	) t_a
	LEFT JOIN ( {querySql} ) t_b ON t_a.id = t_b.f_paper_id
",
log.info("{querySql}"),
querySql = "
	SELECT
		*
	FROM (
		{querySql}
	) t_
	WHERE
		{data.condition}
",
log.info("{querySql}"),
querySql
