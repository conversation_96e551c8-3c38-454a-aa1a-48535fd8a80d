sql.execSQL("v3_safecheck_sql", "
update t_user_address set f_check_book_id =''
WHERE f_userinfo_id in
{data.condition}
 "),
 sql.execSQL("v3_safecheck_sql", "
 update  t_check_plan_item set f_state = '作废' WHERE id in (
    select tcpi.id
    from t_check_plan_item tcpi
    left join t_check_plan tcp on tcpi.f_plan_id = tcp.id
    left join t_check_book cb on cb.id = tcp.f_book_id
    where tcpi.f_userinfoid in {data.condition} and cb.id = '{data.f_book_id}'
    and tcp.f_create_time >= cb.f_last_run_date and tcp.f_create_time < cb.f_run_date
  )
 "),
{code:200}


