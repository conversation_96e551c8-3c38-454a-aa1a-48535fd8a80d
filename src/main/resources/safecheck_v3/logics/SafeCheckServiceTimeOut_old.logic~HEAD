log.info("ServiceTimeOut{context.f_repairman_id}"),
log.info("ServiceTimeOut{context.cc_base_url}"),

// 获取本地所有安检计划的id/id_back
idBacks = path.action({alias: "getNewCheckPlan", data: {f_checker:context.f_repairman_id}}),
log.info("ServiceTimeOut==>本地idBack: {idBacks}"),

// 提取数据, body是参数，cc_base_url是提取路径
body = {
	data: {
		f_checker: context.f_repairman_id,
		services: idBacks.data
	}
},

// 获得工单变化情况
changed = restTools.action({
	action: "post",
	url: "{context.cc_base_url}/api/af-safecheck/logic/getCheckPlanChanged",
	data: body.toString()
}),
log.info("ServiceTimeOut==>工单变化：{changed}"),

params = {code:200,hasNewMsg:0},

// 保存所有新增工单
inserts = changed.data.inserts,

inserts.each(
	entity.action({
		method: "save",
		entity: "t_check_plan",
		data: row
	}),
	params.hasNewMsg = 1
),

// 删除所有要删除的工单
deletes = changed.data.deletes,
deletes.each(
	sql.action({sql: "
		delete from t_check_plan_item where f_plan_id={row.id}
	", cmd: "cmd"}),
	entity.delete("t_check_plan","{row.id}")
),

// 修改所有需要修改的工单
modifies = changed.data.modifies,

modifies.each(
	sql.action({sql: "
		delete from t_check_plan_item where f_plan_id={row.id}
	", cmd: "cmd"}),
	entity.delete("t_check_plan","{row.id}"),

	// 保存工单变化
	entity.action({
		method: "save",
		entity: "t_check_plan",
		data: row
	})
),

params
