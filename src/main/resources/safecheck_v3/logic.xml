<cfg>
    <logic alias='handleDefectPC' path='handleDefectPC.logic'/>
    <logic alias='pdfContract' path='PdfContract.logic'/>
    <logic alias='enablePlan' path='EnablePlan.logic'/>
    <logic alias='enableAddSecurityLevel' path='enableAddSecurityLevel.logic'/>
    <logic alias='SaveEntity' path='SaveEntity.logic'  mobile='true'/>
    <logic alias='DeleteEntity' path='DeleteEntity.logic'  mobile='true'/>
    <logic alias='deleteCheckPlanItem' path='deleteCheckPlanItem.logic'/>
    <!--一键去重安检计划-->
    <logic alias='deleteCheckPlanRepeat' path='deleteCheckPlanRepeat.logic'/>
    <!--选择去重安检计划-->
    <logic alias='SeldeleteCheckPlanRepeat' path='SeldeleteCheckPlanRepeat.logic'/>
    <!--去重后更新用户安检时间-->
    <logic alias='UpdateIssuedTime' path='UpdateIssuedTime.logic'/>

    <logic alias='PhoneStatus' path='PhoneStatus.logic' mobile='true'/>
    <logic alias="getPcCheckPaper" path="getPcCheckPaper.logic"/>
    <logic alias='执行SQL' path='SqlExecute.logic'/>
    <!--	<logic alias='任务分发' path='任务/任务分发.logic'/>-->
    <logic alias='上班签到' path='SignInForWork.logic' mobile='true'/>
    <logic alias='下班签到' path='KnockOff.logic' mobile='true'/>
    <logic alias='修改密码' path='ChangePassword.logic'/>
    <!-- 预约安检单定时器逻辑 -->
    <logic alias='SafecheckYuYueTimeOut' path='SafeCheckYuYueTimeOut.logic' mobile='true'/>
    <!-- 批量上传安检单 -->
    <logic alias='UploadAll' path='UploadAll.logic' mobile='true'/>
    <!-- 上传维修单 -->
    <logic alias='UploadRepairPaper' path='UploadRepairPaper.logic' mobile='true'/>
    <logic alias='更新参数' path='UpdateParam.logic'/>
    <!-- 安检计划生成 -->
    <logic alias='GenCallInPlan' path='GenCallInPlan.logic'/>
    <!-- 安检计划生成 -->
    <logic alias='GenCallInAllPlan' path='GenCallInAllPlan.logic'/>
    <!-- 安检下发中添加item -->
    <logic alias='GenPlanItemAdd' path='GenPlanItemAdd.logic'/>
    <!-- 预约安检计划 -->
    <logic alias='BespeakCheckPlan' path='BespeakCheckPlan.logic'/>
    <!-- 新安检计划下发 -->
    <logic alias='NewBespeakCheckPlan' path='NewBespeakCheckPlan.logic'/>
    <logic alias='ChangeCheckplan' path='CheckPlanchange.logic'/>
    <!--计划调整 -->
    <logic alias='CheckerPlanAdjust' path='CheckerPlanAdjust.logic'/>
    <logic alias='通用更新' path='GenericUpdate.logic'/>
    <logic alias='FetchCheckPaper' path='FetchCheckPaper.logic' mobile='true'/>
    <logic alias='提交档案修改' path='SubmitUserFileUpdate.logic'/>
    <logic alias='审核档案修改' path='ApproveUserFileUpdate.logic'/>
    <logic alias='提取基表读数' path='FetchLastMeterReading.logic'/>
    <logic alias='ApprovePaper' path='ApprovePaper.logic' />
    <logic alias='审核维修单' path='ApproveRepair.logic' />
    <logic alias='DisapprovePaper' path='DisapprovePaper.logic'/>
    <logic alias='保存表档案' path='SaveMeterfile.logic' />
    <logic alias='修改表档案' path='UpdateMeterfile.logic' />
    <logic alias='删除表档案' path='DelMeterfile.logic' />
    <logic alias='人工录入' path='dealuserfiles.logic' />
    <logic alias='人工录入修改' path='dealuserfileschange.logic' />
    <logic alias='人工录入删除' path='dealuserfilesdel.logic' />
    <logic alias='档案状态修改' path='WriteOffUserFile.logic' />
    <logic alias='现场修改电话' path='ModifyUserPhone.logic' />
    <logic alias='现场修改表号' path='ModifyMeterNo.logic' />
    <!-- 手机端登录 -->
    <logic alias='getOtherParam' path='getOtherParam.logic' />
    <logic alias='修改安检用户信息' path='EditUserInfo.logic' />
    <!-- 移动端批量上传 -->
    <logic alias='添加总隐患信息' path='createDefect.logic' />
    <!-- 下载安检计划 -->
    <logic alias='CheckoutPlan' path='CheckoutPlan.logic' mobile='true'/>
    <!-- 获取安检计划改变 -->
    <logic alias='getCheckPlanChanged' path='getCheckPlanChanged.logic' mobile='true'/>
    <!-- 定时提取 -->
    <logic alias='SafeCheckTimeOut' path='SafeCheckTimeOut.logic' mobile='true'/>
    <!-- 提取安检 -->
    <logic alias='SafeCheckServiceTimeOut' path='SafeCheckServiceTimeOut.logic' mobile='true'/>
    <!-- 刷新 -->
    <logic alias='update' path='update.logic' mobile='true'/>
    <!-- 安检预约 -->
    <logic alias='BookingCheck' path='BookingCheck.logic' mobile='true'/>
    <!-- 上传维修单 -->
    <logic alias='上传维修单' path='UploadRepair.logic' mobile='true'/>
    <!-- 提取安检计划 -->
    <logic alias='提取安检计划' path='GetCheckPlan.logic' mobile='true'/>
    <!-- 修改密码 -->
    <!--	<logic alias='EditPassword' path='EditPassword.logic'/>-->
    <!-- 提取安检通知 -->
    <logic alias='提取安检通知' path='GetCheckNotice.logic' mobile='true'/>
    <!-- 获取安检设备信息 -->
    <logic alias='获取安检设备' path='GetDevices.logic' mobile='true'/>
    <!-- 获取安检设备信息 -->
    <logic alias='获取隐患信息' path='GetDefects.logic' mobile='true'/>
    <!-- 保存安检单信息 -->
    <logic alias='保存安检单信息' path='SavePaper.logic' mobile='true'/>
    <!-- 保存无计划安检单信息 -->
    <logic alias='SafeSaveNoPaper' path='SaveNoPaper.logic' mobile='true'/>
    <!-- 上传安检信息 -->
    <logic alias='上传安检信息' path='UpCheckPaper.logic' mobile='true'/>
    <!-- 更新安检信息 -->
    <logic alias='UpdateCheckPaper' path='UpdateCheckPaper.logic' mobile='true'/>
    <!-- 删除安检计划 -->
    <logic alias='deleteCheckPlan' path='deleteCheckPlan.logic'/>
    <!-- PC端预约安检同步 -->
    <logic alias='PCbookingCheck' path='PCbookingCheck.logic' mobile='true'/>
    <!-- 查询安检单信息 -->
    <logic alias='getPaperInfo' path='getPaperInfo.logic' mobile='true'/>
    <!-- 保存设备信息 -->
    <logic alias='saveDevice' path='saveDevice.logic' mobile='true'/>
    <!-- 上传设备信息 -->
    <logic alias='UpCheckPaperDevices' path='UpCheckPaperDevices.logic' mobile='true'/>
    <!-- 上传安检设备信息 -->
    <logic alias='updateDevice' path='updateDevice.logic' mobile='true'/>
    <!-- 无计划安检打回 -->
    <logic alias='NoPlanDisapprove' path='NoPlanDisapprove.logic' mobile='true'/>

    <logic alias='getUserInfo' path='getUserInfo.logic' mobile='true'/>
    <!-- 安检转维修 -->
    <logic alias='safecheckToRepair' path='safecheckToRepair.logic' mobile='true'/>
    <!-- 更新用户信息 -->
    <logic alias='UpdateUserinfo' path='UpdateUserinfo.logic' />
    <!-- 获取安检周期 -->
    <logic alias='getSafeCheckCycle' path='getSafeCheckCycle.logic'/>
    <!-- 下载共享计划 -->
    <logic alias='DownCheckPlanInfo' path='DownCheckPlanInfo.logic' mobile='true'/>
    <!-- 获取服务器端共享计划 -->
    <logic alias='GetCheckPlanIntoToServer' path='GetCheckPlanIntoToServer.logic'/>
    <!-- 上传成功后修改本地安检单状态 -->
    <logic alias='FinishUpload' path='FinishUpload.logic' mobile='true'/>
    <!-- 上传成功后修改本地安检单状态 -->
    <logic alias='getLiveDispose' path='getLiveDispose.logic' mobile='true'/>
    <!-- 上传成功后修改户档案安检结果 -->
    <logic alias='UploadUserInfo' path='UploadUserInfo.logic'/>
    <!-- 安检隐患结果统计 -->
    <logic alias='statistics' path='statistics.logic'/>
    <!-- 更新共享安检计划 -->
    <logic alias='FreshPlanItem' path='FreshPlanItem.logic' mobile='true'/>
    <!-- 获取共享安检计划变化项 -->
    <logic alias='getPlanItemChanged' path='getPlanItemChanged.logic'/>
    <!-- 维修结果回调接口 -->
    <logic alias='RepaireResult' path='RepaireResult.logic' auth="false"/>
    <!-- 安检结果按月统计接口 -->
    <logic alias='SearchByMonth' path='SearchByMonth.logic'/>
    <logic alias='updatePaperRepair' path='updatePaperRepair.logic'/>
    <logic alias='SafecheckFetchFootprint' path='SafecheckFetchFootprint.logic'/>

    <!--根据条件查询抄表册信息-->
    <logic alias='getMeterBooksData' path='getMeterBooksData.logic'/>
    <!--根据条件生成查询sql-->
    <logic alias='ConditionalGeneration' path='ConditionalGeneration.logic'/>
    <!--根据条件生成查询sql-->
    <logic alias='GeneratedMessage' path='GeneratedMessage.logic'/>
    <!--生成安检计划项-->
    <logic alias='AddCheckPlanItem' path='AddCheckPlanItem.logic'/>
    <logic alias='imagesUploadAndroid' path='imagesUploadAndroid.logic' mobile="true"/>
    <!--修改安检计划项-->
    <logic alias='UpCheckPlanItem' path='UpCheckPlanItem.logic'/>
    <!--修改手机端本地安检计划-->
    <logic alias='UpCheckPlanItemAndroid' path='UpCheckPlanItemAndroid.logic' mobile="true"/>
    <!--安检抽单-->
    <logic alias="ExtractCheckPlanItem" path="ExtractCheckPlanItem.logic"/>
    <!--修改安检计划项状态-->
    <logic alias='UpCheckPlanItemState' path='UpCheckPlanItemState.logic'/>
    <!--生成安检计划-->
    <logic alias='createCheckPlan' path='createCheckPlan.logic'/>
    <!--定时器生成安检计划-->
    <logic alias='createCheckPlan1' path='createCheckPlan1.logic'/>
    <!-- 导出excle -->
    <logic alias='SafeExportfile' path='SafeExportfile.logic' dataSource="slave"/>
    <!-- 导出excle -->
    <logic alias='SafeExportExcel' path='SafeExportExcel.logic'/>
    <!--pc端处理隐患-->
    <logic alias="pcRepairDefect" path="pcRepairDefect.logic"/>
    <!--转维修后更新安检单-->
    <logic alias="updateDefectState" path="updateDefectState.logic"/>
    <!--修改安检计划-->
    <logic alias="updateCheckPlan" path="UpdateCheckPlan.logic"/>
    <!--补录-->
    <logic alias="SuppCheckPaper" path="SuppCheckPaper.logic"/>
    <!--获取隐患项展示隐患级别-->
    <logic alias="GetDefectPaperWithDefectLevel" path="GetDefectPaperWithDefectLevel.logic"/>

    <!--高密手机-->
    <!--获取系统公告-->
    <logic alias="getBulletin" path="gaomi/bulletin/getBulletin.logic"/>
    <!--发布系统公告-->
    <logic alias="addBulletin" path="gaomi/bulletin/addBulletin.logic"/>
    <!--查询用户信息-->
    <logic alias="gaoMiGetUserInfo" path="gaomi/search/getUserInfo.logic"/>
    <!--查询缴费信息-->
    <logic alias="getSelling" path="gaomi/search/getSelling.logic"/>
    <!--查询抄表信息-->
    <logic alias="getHandplan" path="gaomi/search/getHandplan.logic"/>
    <!--查询安检信息-->
    <logic alias="getCheckPaper" path="gaomi/search/getCheckPaper.logic"/>
    <!--高密报警信息接口-->
    <logic alias="getAlarm" path="gaomi/gaomi/getAlarm.logic" mobile="true"/>
    <!--高密报表列表接口-->
    <logic alias="getReportList" path="gaomi/gaomi/getReportList.logic"/>
    <!--高密工艺图列表接口-->
    <logic alias="getCraftList" path="gaomi/gaomi/getCraftList.logic"/>
    <!--高密数据展示图列表接口-->
    <logic alias="getDataList" path="gaomi/gaomi/getDataList.logic"/>
    <!--获取系统公告-->
    <logic alias="getFeedback" path="gaomi/bulletin/getFeedback.logic"/>
    <!--发布系统公告-->
    <logic alias="addFeedback" path="gaomi/bulletin/addFeedback.logic"/>
    <!--信息上传-->
    <logic alias="infoUploadToServer" path="gaomi/search/infoUploadToServer.logic"/>
    <!--手机信息上传-->
    <logic alias="phoneInfoUpload" path="gaomi/search/phoneInfoUpload.logic" mobile="true"/>
    <!--手机信息上传保存-->
    <logic alias="saveUploadInfo" path="gaomi/search/saveUploadInfo.logic" mobile="true"/>
    <!--手机查询本地保存的信息-->
    <logic alias="phoneGetUploadInfo" path="gaomi/search/PhoneGetUploadInfo.logic" mobile="true"/>
    <!--手机端更新保存的上传数据-->
    <logic alias="updateUploadInfo" path="gaomi/search/UpdateUploadInfo.logic" mobile="true"/>
    <!--非民用自动生成计划 -->
    <logic alias="AddPlanNoCivil" path="AddPlanNoCivil.logic"/>
    <logic alias="OnePlanOneCheck" path="OnePlanOneCheck.logic"/>
    <logic alias="QueryChecker" path="QueryChecker.logic"/>
    <logic alias="CheckCollect" path="CheckCollect.logic"/>
    <!-- 安检修改用户电话-->
    <logic alias="safecheckUpdatePhone" path="safecheckUpdatePhone.logic"/>
    <logic alias="getShareCheckPlanLogic" path="getShareCheckPlan.logic"/>
    <logic alias="getShareCheckPlan" path="getShareCheckPlan.logic"/>
    <logic alias="searchNoPlanItem" path="searchNoPlanItem.logic" mobile="true"/>
    <logic alias="onlyQuery" path="onlyQuery.logic"/>
    <logic alias="EntryStatusNoMeet" path="EntryStatusNoMeet.logic"/>
    <logic alias="checkPlanTimer" path="checkPlanTimer.logic"/>
    <!--查找安检完成但未发送短信的安检单-->
    <logic alias="queryMessPaper" path="queryMessPaper.logic"/>
    <logic alias="TimeCreatePlan" path="TimeCreatePlan.logic"/>
    <!-- 导出logic -->
    <logic alias="safeGetExportProgress" path="safeGetExportProgress.logic"/>
    <!-- 新增、修改安检周期 -->
    <logic alias="safe_savecircle" path="safe_savecircle.logic"/>
    <!--发布/更新/删除 公告 -->
    <logic alias="safe_UpdateAnnouncement" path="UpdateAnnouncement.logic"/>
    <!--获取指定路径下excel中的数据-->
    <logic alias="safe_GetUserInfoFromExcel" path="safe_GetUserInfoFromExcel.logic"/>
    <!--隐患图表-->
    <logic alias="hiddenchart" path="hiddenchart.logic"/>
    <!--巡检本地保存-->
    <logic alias="SaveNoInspection" path="inspection/SaveNoInspection.logic" mobile="true"/>
    <!--巡检上传-->
    <logic alias="UpdateInspectionPaper" path="inspection/UpdateInspectionPaper.logic"/>
    <!--巡检本地上传-->
    <logic alias="UpInspectionPaper" path="inspection/UpInspectionPaper.logic" mobile="true"/>
    <!--巡检本地上传-->
    <logic alias="InsUploadAll" path="inspection/InsUploadAll.logic" mobile="true"/>
    <!--第三方接口查询用户累够及购气记录-->
    <logic alias="SaUserMeterGasInfo" path="GetUserSaleInfo/SaUserMeterGasInfo.logic"/>
    <!--第三方接口查询用户累够及购气记录-->
    <logic alias="SaUserSellingGasInfo" path="GetUserSaleInfo/SaUserSellingGasInfo.logic"/>
    <!--第三方预约安检生成安检单-->
    <logic alias='CreatPlanOneCheck' path='CreatPlanOneCheck.logic'/>
    <logic alias='wechat_CreatPlanOneCheck' path='CreatPlanOneCheck.logic' auth="false"/>
    <!--第三方预约安检生成安检单-->
    <logic alias='OutCreateCheckPlan' path='OutCreateCheckPlan.logic'/>
    <!--第三方预约安检生成安检单-->
    <logic alias='OutAddCheckPlanItem' path='OutAddCheckPlanItem.logic'/>
    <!--定时器自动更新回写安检单	-->
    <logic alias='AutoChangeUserFiles' path='AutoChangeUserFiles.logic'/>

    <!--作废安检单	-->
    <logic alias='DelCheckPaper' path='DelCheckPaper.logic'/>
    <!--手机端上传位置信息到本地，需要单独部署一个服务进行上传点服务-->
    <!--	<logic alias="UploadPhoneStatus" path="mapLocation/UploadPhoneStatus.logic" />-->
    <logic alias="completeDefectDeal" path="completeDefectDeal.logic" />
    <logic alias="safe_dealWithDefect" path="dealWithDefect.logic" mobile="true" />

    <logic alias="safe_upUserinfoApprove" path="safe_upUserinfoApprove.logic" mobile="true" />
    <!--安检批量转维修-->
    <logic alias="UploadRectification" path="UploadRectification.logic"  />
    <!--修改隐患项状态-->
    <logic alias="updateDefectStateZG" path="updateDefectStateZG.logic"  />

    <logic alias="AddCheckBook" path="AddCheckBook.logic"  />
    <logic alias="safeEntity" path="entityPartialSave.logic"  />

    <logic alias="TipConfig" path="TipConfig.logic"  />

    <!--温宿数据同步接口-->
    <!--	<logic alias="AddCheckSynchronization" path="AddCheckSynchronization.logic"  />-->
    <!--查询某年安检派发任务的完成情况接口-->
    <logic alias="getCheckPlanStatistic" path="getCheckPlanStatistic.logic"  />
    <!--获取安检状态统计数据接口-->
    <logic alias="GetSecurityCheckStatisticscount" path="SecurityCheckInterface/GetSecurityCheckStatisticscount.logic"/>
    <!--获取安检类型数据接口-->
    <logic alias="GetSecurityCheckTypedatacount" path="SecurityCheckInterface/GetSecurityCheckTypedatacount.logic"/>
    <!--获取安检总户数接口-->
    <logic alias="GetTotalNumberCheckpointscounts" path="SecurityCheckInterface/GetTotalNumberCheckpointscounts.logic"/>
    <!--获取安检隐患分布情况接口-->
    <logic alias="ObtainDistributionSecurityRiskscount" path="SecurityCheckInterface/ObtainDistributionSecurityRiskscount.logic"/>
    <!--获取安检员任务情况数据接口完成前五-->
    <logic alias="ObtainTaskStatusDataSecurityInspectorcount" path="SecurityCheckInterface/ObtainTaskStatusDataSecurityInspectorcount.logic"/>
    <!--获取安检员任务情况数据接口未完成前五-->
    <logic alias="ObtainChecktaskSituationUnfinishedcount" path="SecurityCheckInterface/ObtainChecktaskSituationUnfinishedcount.logic"/>

    <!--新隐患查看-->
    <logic alias="pcRepairDefectNew" path="defect/pcRepairDefectNew.logic"  />
    <logic alias="updateDefectStateNew" path="defect/updateDefectStateNew.logic"  />
    <logic alias="DefectAuditUpdate" path="defect/DefectAuditUpdate.logic"  />


    <!--手机单户转维修-->
    <logic alias="phoneUploadRectification" path="phoneUploadRectification.logic"/>

    <logic alias="updateCheckBook" path="updateCheckBook.logic"  />

    <!--获取安检工单报建待办数量-->
    <logic alias="getModuleUpcoming" path="getModuleUpcoming.logic"  />
    <!--二维码工具类接口-->
    <logic alias="QrcodeUtil" path="QrcodeUtil.logic"  />
    <!--上传base64照片-->
    <logic alias="UploadBase64" path="UploadBase64.logic"  />
    <!--燃气表照片AI识别-->
    <logic alias="CheckPaperOCR" path="CheckPaperOCR.logic"  />
    <!--年度清除planid业务逻辑	-->
    <logic alias="clearPlanId" path="clearPlanId.logic"  />
    <!--取消拉黑定时器	-->
    <logic alias="safe_CancelBack" path="CancelBackList.logic"  />
    <logic alias="ChangeLocalPhoneState" path="mapLocation/ChangeLocalPhoneState.logic" mobile="true" />
    <logic alias="UpPhoneStatus" path="mapLocation/UpPhoneStatus.logic" mobile="true" />
    <!--查询本地点集进行汇总纠偏  定位服务需要的logic  更新定位服务请放开-->
    <!--<logic alias="queryLocalLocationStatus" path="mapLocation/queryLocalLocationStatus.logic" mobile="true" />
    &lt;!&ndash;本地上传到服务器并删除本地logic&ndash;&gt;
    <logic alias="UpLocalLocationToRemote" path="mapLocation/UpLocalLocationToRemote.logic" mobile="true" />
    &lt;!&ndash;// 将手机端的轨迹数据，上传到服务器保存JSON文件，并将路径保存到数据库的字段中&ndash;&gt;
    <logic alias="UpLoadToRemote" path="mapLocation/UpLoadToRemote.logic" />
    &lt;!&ndash; 按分公司和时间查询用户轨迹信息&ndash;&gt;
    <logic alias="QueryUserTracksByCompany" path="mapLocation/QueryUserTracksByCompany.logic" />
    &lt;!&ndash;// 根据数据库记录，读取本地轨迹JSON文件，并将格式转换返回前台&ndash;&gt;
    <logic alias="queryRemoteLocationMapData" path="mapLocation/queryRemoteLocationMapData.logic" />
    &lt;!&ndash;手机端实时位置上传逻辑&ndash;&gt;
    <logic alias="LocalPhoneStatus" path="mapLocation/LocalPhoneStatus.logic" mobile="true" />-->
    <!-- 根据工单编号查询是否有隐患,无隐患则解除限购 -->
    <logic alias="CancelLimited" path="CancelLimited.logic"/>
    <!-- 获取手机状态 -->
    <logic alias="getPhoneState" path="mapLocation/getPhoneState.logic"/>
    <!-- 保存手机状态 -->
    <logic alias="savePhoneState" path="mapLocation/savePhoneState.logic"/>
    <!-- 微信审核用户自行整改通过回调接口 -->
    <logic alias="ZxRemoveDefect" path="ZxRemoveDefect.logic"/>
    <!-- 预约安检页面审核结果调用接口 -->
    <logic alias="amendOrderApply" path="amendOrderApply.logic"/>
    <logic alias="saveWorkRecord" path="saveWorkRecord.logic"/>
    <!-- 渭南限制购气定时器调用 -->
    <logic alias="WeiNanTaskTimer" path="WeiNanTaskTimer.logic"/>
    <logic alias="SafeUploadSenWx" path="SafeUploadSenWx.logic"/>
    <logic alias="TaskTimer" path="TaskTimer.logic"/>
    <!-- 渭南特殊需求，协助安检 -->
    <logic alias="UpPlanItemChecker" path="UpPlanItemChecker.logic"/>
    <logic alias="updateDefectTime" path="updateDefectTime.logic" />
    <!--	安检结果查看页面 审核-->
    <logic alias="updateAafeAudit" path="UpdateSafeAudit.logic"/>
    <!--安检册定时-->
    <logic alias="AutomaticDelivery" path="AutomaticDelivery.logic"/>
    <logic alias="MoveInPlan" path="MoveInPlan.logic"/>
    <logic alias="removeUserByCheckBook" path="removeUserByCheckBook.logic"/>
    <logic alias="removesCheckBookUser" path="removesCheckBookUser.logic"/>
    <logic alias='imgUpload' path='imgUpload.logic' mobile='true'/>
    <logic alias='getUserinfomation' path='getUserinfomation.logic'/>
    <!--根据安检册创建安检计划-->
    <logic alias='CreatorBookplan' path='CreatorBookplan.logic'/>
    <logic alias='ReCheckToRepairV4' path='ReCheckToRepairV4.logic'/>
    <!--	v4安检抽检-荣创-->
    <logic alias='updateSpotCheck' path='updateSpotCheck.logic' mobile='true'/>
    <!--定时器，自动转维修，自动限购-->
    <logic alias="autoToRepairWithBlack" path="AutoToRepairWithBlack.logic"/>
    <!--由于V3manage中导出获取进度已停，把获取进去logic放到V3安检中-->
    <logic alias="getBatchOperaPro" path="getBatchOperaPro.logic"/>
    <!--照片重传-->
    <logic alias="photoRetransmission" path="PhotoRetransmission.logic" mobile='true' />
    <!-- 公共导出 -->
    <logic alias="exportfile" path='common/exportfile.logic' auth="false" dataSource="slave"/>

    <!-- ************************************path 转logic*********************************************** -->
    <!--获取安检单内容-->
    <logic alias='path_getCheckPaper' path='pathConvertor/getCheckPaper.logic'/>
    <!--兼容手机端获取安检单内容(其他业务勿用)-->
    <logic alias='path_getPhoneCheckPaper' path='pathConvertor/getPhoneCheckPaper.logic' mobile="true"/>
    <!--获取手机端安检待上传列表-->
    <logic alias='path_getUploadPaperList' path='pathConvertor/getUploadPaperList.logic' mobile="true"/>
    <!--查询手机本地计划列表-->
    <logic alias='getLocalShareCheckPlan' path='getLocalShareCheckPlan.logic' mobile="true"/>
    <!--安检册创建计划并添加用户-->
    <logic alias='createAndAddPlan' path='createAndAddPlan.logic'/>
    <!-- **********************************手机端设置相关逻辑（从systemModules迁移过来）************************* -->
    <logic alias="定时提取" path='phoneConfigLogic/Timeout.logic' mobile='true'/>
    <logic alias="clearPhoneData" path='phoneConfigLogic/clearPhoneData.logic' mobile='true'/>
    <logic alias="httpGetInitData" path='phoneConfigLogic/httpGetInitData.logic' mobile='true'/>
    <logic alias="sealBind" path="sealBind.logic" mobile="true"/>
    <logic alias="saveSealBind" path="saveSealBind.logic"/>
</cfg>
