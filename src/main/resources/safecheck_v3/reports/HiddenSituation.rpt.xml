<report>
  <sql name="HiddenSituation" sql="HiddenSituation" param="{startDate:startDate,endDate:endDate,f_filialeid:f_filialeid,condition:condition}" />
  <sql name="getDefectType" sql="getDefectType" param="{startDate:startDate,endDate:endDate,f_filialeid:f_filialeid,condition:condition}" />
  <column number="0" width="100" startx="0" />
  <column number="1" width="500" startx="100" />
  <column number="2" width="100" startx="600" />
  <column number="3" width="100" startx="700" />
  <column number="4" width="100" startx="800" />
  <column number="5" width="100" startx="900" />
  <column number="6" width="100" startx="1000" />
  <row number="0" height="30" starty="0" />
  <row number="1" height="30" starty="30" />
  <row number="2" height="30" starty="60" />
  <row number="3" height="30" starty="90" />
  <reportblock row="0" column="0" rowspan="4" columnspan="7" content="" css="" width="1100" height="120">
    <headblock row="0" column="0" rowspan="1" columnspan="7" content="" css="" width="1100" height="30" name="">
      <cell row="0" column="0" rowspan="1" columnspan="1" content="$隐患设备$" css="tdcenter report-head head-font" width="100" height="30" />
      <cell row="0" column="1" rowspan="1" columnspan="1" content="$隐患明细$" css="tdcenter report-head head-font" width="500" height="30" />
      <cell row="0" column="2" rowspan="1" columnspan="1" content="$隐患级别$" css="tdcenter report-head head-font" width="100" height="30" />
      <cell row="0" column="3" rowspan="1" columnspan="1" content="$隐患发生(起)$" css="tdcenter report-head head-font" width="100" height="30" />
      <cell row="0" column="4" rowspan="1" columnspan="1" content="$未处理$" css="tdcenter report-head head-font" width="100" height="30" />
      <cell row="0" column="5" rowspan="1" columnspan="1" content="$处理中$" css="tdcenter report-head head-font" width="100" height="30" />
      <cell row="0" column="6" rowspan="1" columnspan="1" content="$已处理$" css="tdcenter report-head head-font" width="100" height="30" />
    </headblock>
    <leftblock row="1" column="0" rowspan="3" columnspan="2" content="" css="" width="600" height="90" name="">
      <leftblock row="1" column="0" rowspan="2" columnspan="2" content="getDefectType" css="" width="600" height="60" name="left">
        <head row="1" column="0" rowspan="2" columnspan="1" content="left.f_device_type" css="tdcenter report-left head-font" width="100" height="60" />
        <leftblock row="1" column="1" rowspan="1" columnspan="1" content="HiddenSituation.where(row.f_device_type==left.f_device_type)" css="" width="500" height="30" name="left1">
          <head row="1" column="1" rowspan="1" columnspan="1" content="left1.f_item_value" css="tdcenter report-left head-font" width="500" height="30" />
        </leftblock>
        <cell row="2" column="1" rowspan="1" columnspan="1" content="$小计$" css="tdcenter report-left head-font" width="500" height="30" />
      </leftblock>
      <cell row="3" column="0" rowspan="1" columnspan="2" content="$总计$" css="tdcenter report-left head-font" width="600" height="30" />
    </leftblock>
    <bodyblock row="1" column="2" rowspan="3" columnspan="5" content="" css="" width="500" height="90" headexpression="" leftexpression="" headname="" leftname="">
      <bodyblock row="1" column="2" rowspan="2" columnspan="5" content="" css="" width="500" height="60" headexpression="" leftexpression="getDefectType" headname="" leftname="left">
        <bodyblock row="1" column="2" rowspan="1" columnspan="5" content="" css="" width="500" height="30" headexpression="" leftexpression="HiddenSituation.where(row.f_device_type==left.f_device_type)" headname="" leftname="left1">
          <cell row="1" column="2" rowspan="1" columnspan="1" content="left1.f_defect_level" css="tdcenter report-main main-font" width="100" height="30" />
          <cell row="1" column="3" rowspan="1" columnspan="1" content="left1.hiddennum" css="tdcenter report-main main-font" width="100" height="30" />
          <cell row="1" column="4" rowspan="1" columnspan="1" content="left1.weichuli" css="tdcenter report-main main-font" width="100" height="30" />
          <cell row="1" column="5" rowspan="1" columnspan="1" content="left1.zhuanweixiu" css="tdcenter report-main main-font" width="100" height="30" />
          <cell row="1" column="6" rowspan="1" columnspan="1" content="left1.yichuli" css="tdcenter report-main main-font" width="100" height="30" />
        </bodyblock>
        <cell row="2" column="2" rowspan="1" columnspan="1" content="" css="tdcenter report-main main-font" width="100" height="30" />
        <cell row="2" column="3" rowspan="1" columnspan="1" content="HiddenSituation.where(row.f_device_type==left.f_device_type).sum(row.hiddennum)" css="tdcenter report-main main-font" width="100" height="30" />
        <cell row="2" column="4" rowspan="1" columnspan="1" content="HiddenSituation.where(row.f_device_type==left.f_device_type).sum(row.weichuli)" css="tdcenter report-main main-font" width="100" height="30" />
        <cell row="2" column="5" rowspan="1" columnspan="1" content="HiddenSituation.where(row.f_device_type==left.f_device_type).sum(row.zhuanweixiu)" css="tdcenter report-main main-font" width="100" height="30" />
        <cell row="2" column="6" rowspan="1" columnspan="1" content="HiddenSituation.where(row.f_device_type==left.f_device_type).sum(row.yichuli)" css="tdcenter report-main main-font" width="100" height="30" />
      </bodyblock>
      <cell row="3" column="2" rowspan="1" columnspan="1" content="" css="tdcenter report-main main-font" width="100" height="30" />
      <cell row="3" column="3" rowspan="1" columnspan="1" content="HiddenSituation.sum(row.hiddennum)" css="tdcenter report-main main-font" width="100" height="30" />
      <cell row="3" column="4" rowspan="1" columnspan="1" content="HiddenSituation.sum(row.weichuli)" css="tdcenter report-main main-font" width="100" height="30" />
      <cell row="3" column="5" rowspan="1" columnspan="1" content="HiddenSituation.sum(row.zhuanweixiu)" css="tdcenter report-main main-font" width="100" height="30" />
      <cell row="3" column="6" rowspan="1" columnspan="1" content="HiddenSituation.sum(row.yichuli)" css="tdcenter report-main main-font" width="100" height="30" />
    </bodyblock>
  </reportblock>
</report>