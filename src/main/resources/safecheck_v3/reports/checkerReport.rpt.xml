<report>
  <sql name="HiddenSituation" sql="CheckerReportSql2" param="{condition:condition}" />
  <sql name="getDefectType" sql="CheckerReportSql1" param="{condition:condition}" />
  <column number="0" width="100" startx="0" />
  <column number="1" width="100" startx="100" />
  <column number="2" width="100" startx="200" />
  <column number="3" width="100" startx="300" />
  <column number="4" width="100" startx="400" />
  <column number="5" width="100" startx="500" />
  <column number="6" width="100" startx="600" />
  <column number="7" width="100" startx="700" />
  <column number="8" width="100" startx="800" />
  <column number="9" width="100" startx="900" />
  <column number="10" width="100" startx="1000" />
  <row number="0" height="30" starty="0" />
  <row number="1" height="30" starty="30" />
  <row number="2" height="30" starty="60" />
  <row number="3" height="30" starty="90" />
  <reportblock row="0" column="0" rowspan="4" columnspan="11" content="" css="" width="1100" height="120">
    <headblock row="0" column="0" rowspan="1" columnspan="11" content="" css="" width="1100" height="30" name="">
      <cell row="0" column="0" rowspan="1" columnspan="1" content="$小区名称$" css="tdcenter report-head head-font" width="100" height="30" />
      <cell row="0" column="1" rowspan="1" columnspan="1" content="$安检员$" css="tdcenter report-head head-font" width="100" height="30" />
      <cell row="0" column="2" rowspan="1" columnspan="1" content="$计划安检总户数$" css="tdcenter report-head head-font" width="100" height="30" />
      <cell row="0" column="3" rowspan="1" columnspan="1" content="$未检户数$" css="tdcenter report-head head-font" width="100" height="30" />
      <cell row="0" column="4" rowspan="1" columnspan="1" content="$已检户数$" css="tdcenter report-head head-font" width="100" height="30" />
      <cell row="0" column="5" rowspan="1" columnspan="1" content="$入户户数$" css="tdcenter report-head head-font" width="100" height="30" />
      <cell row="0" column="6" rowspan="1" columnspan="1" content="$三次到访不遇户数$" css="tdcenter report-head head-font" width="100" height="30" />
      <cell row="0" column="7" rowspan="1" columnspan="1" content="$拒检户数$" css="tdcenter report-head head-font" width="100" height="30" />
      <cell row="0" column="8" rowspan="1" columnspan="1" content="$安检率$" css="tdcenter report-head head-font" width="100" height="30" />
      <cell row="0" column="9" rowspan="1" columnspan="1" content="$申请安检户数$" css="tdcenter report-head head-font" width="100" height="30" />
      <cell row="0" column="10" rowspan="1" columnspan="1" content="$总入户数$" css="tdcenter report-head head-font" width="100" height="30" />
    </headblock>
    <leftblock row="1" column="0" rowspan="3" columnspan="2" content="" css="" width="200" height="90" name="">
      <leftblock row="1" column="0" rowspan="2" columnspan="2" content="getDefectType" css="" width="200" height="60" name="left">
        <head row="1" column="0" rowspan="2" columnspan="1" content="left.area" css="tdcenter report-left head-font" width="100" height="60" />
        <leftblock row="1" column="1" rowspan="1" columnspan="1" content="HiddenSituation.where(row.area==left.area)" css="" width="100" height="30" name="left1">
          <head row="1" column="1" rowspan="1" columnspan="1" content="left1.checker" css="tdcenter report-left head-font" width="100" height="30" />
        </leftblock>
        <cell row="2" column="1" rowspan="1" columnspan="1" content="$小计$" css="tdcenter report-left head-font" width="100" height="30" />
      </leftblock>
      <cell row="3" column="0" rowspan="1" columnspan="2" content="$总计$" css="tdcenter report-left head-font" width="200" height="30" />
    </leftblock>
    <bodyblock row="1" column="2" rowspan="3" columnspan="9" content="" css="" width="900" height="90" headexpression="" leftexpression="" headname="" leftname="">
      <bodyblock row="1" column="2" rowspan="2" columnspan="9" content="" css="" width="900" height="60" headexpression="" leftexpression="getDefectType" headname="" leftname="left">
        <bodyblock row="1" column="2" rowspan="1" columnspan="9" content="" css="" width="900" height="30" headexpression="" leftexpression="HiddenSituation.where(row.area==left.area)" headname="" leftname="left1">
          <cell row="1" column="2" rowspan="1" columnspan="1" content="left1.data1" css="tdcenter report-main main-font" width="100" height="30" />
          <cell row="1" column="3" rowspan="1" columnspan="1" content="left1.data2" css="tdcenter report-main main-font" width="100" height="30" />
          <cell row="1" column="4" rowspan="1" columnspan="1" content="left1.data3" css="tdcenter report-head head-font" width="100" height="30" />
          <cell row="1" column="5" rowspan="1" columnspan="1" content="left1.data4" css="tdcenter report-head head-font" width="100" height="30" />
          <cell row="1" column="6" rowspan="1" columnspan="1" content="left1.data5" css="tdcenter report-main main-font" width="100" height="30" />
          <cell row="1" column="7" rowspan="1" columnspan="1" content="left1.data6" css="tdcenter report-main main-font" width="100" height="30" />
          <cell row="1" column="8" rowspan="1" columnspan="1" content="left1.data7" css="tdcenter report-main main-font" width="100" height="30" />
          <cell row="1" column="9" rowspan="1" columnspan="1" content="left1.data8" css="tdcenter report-main main-font" width="100" height="30" />
          <cell row="1" column="10" rowspan="1" columnspan="1" content="left1.data9" css="tdcenter report-main main-font" width="100" height="30" />
        </bodyblock>
        <cell row="2" column="2" rowspan="1" columnspan="1" content="HiddenSituation.where(row.area==left.area).sum(row.data1)" css="tdcenter report-main main-font" width="100" height="30" />
        <cell row="2" column="3" rowspan="1" columnspan="1" content="HiddenSituation.where(row.area==left.area).sum(row.data2)" css="tdcenter report-main main-font" width="100" height="30" />
        <cell row="2" column="4" rowspan="1" columnspan="1" content="HiddenSituation.where(row.area==left.area).sum(row.data3)" css="tdcenter report-head head-font" width="100" height="30" />
        <cell row="2" column="5" rowspan="1" columnspan="1" content="HiddenSituation.where(row.area==left.area).sum(row.data4)" css="tdcenter report-head head-font" width="100" height="30" />
        <cell row="2" column="6" rowspan="1" columnspan="1" content="HiddenSituation.where(row.area==left.area).sum(row.data5)" css="tdcenter report-main main-font" width="100" height="30" />
        <cell row="2" column="7" rowspan="1" columnspan="1" content="HiddenSituation.where(row.area==left.area).sum(row.data6)" css="tdcenter report-main main-font" width="100" height="30" />
        <cell row="2" column="8" rowspan="1" columnspan="1" content="" css="tdcenter report-main main-font" width="100" height="30" />
        <cell row="2" column="9" rowspan="1" columnspan="1" content="HiddenSituation.where(row.area==left.area).sum(row.data8)" css="tdcenter report-main main-font" width="100" height="30" />
        <cell row="2" column="10" rowspan="1" columnspan="1" content="HiddenSituation.where(row.area==left.area).sum(row.data9)" css="tdcenter report-main main-font" width="100" height="30" />
      </bodyblock>
      <cell row="3" column="2" rowspan="1" columnspan="1" content="HiddenSituation.sum(row.data1)" css="tdcenter report-main main-font" width="100" height="30" />
      <cell row="3" column="3" rowspan="1" columnspan="1" content="HiddenSituation.sum(row.data2)" css="tdcenter report-main main-font" width="100" height="30" />
      <cell row="3" column="4" rowspan="1" columnspan="1" content="HiddenSituation.sum(row.data3)" css="tdcenter report-head head-font" width="100" height="30" />
      <cell row="3" column="5" rowspan="1" columnspan="1" content="HiddenSituation.sum(row.data4)" css="tdcenter report-head head-font" width="100" height="30" />
      <cell row="3" column="6" rowspan="1" columnspan="1" content="HiddenSituation.sum(row.data5)" css="tdcenter report-main main-font" width="100" height="30" />
      <cell row="3" column="7" rowspan="1" columnspan="1" content="HiddenSituation.sum(row.data6)" css="tdcenter report-main main-font" width="100" height="30" />
      <cell row="3" column="8" rowspan="1" columnspan="1" content="" css="tdcenter report-main main-font" width="100" height="30" />
      <cell row="3" column="9" rowspan="1" columnspan="1" content="HiddenSituation.sum(row.data8)" css="tdcenter report-main main-font" width="100" height="30" />
      <cell row="3" column="10" rowspan="1" columnspan="1" content="HiddenSituation.sum(row.data9)" css="tdcenter report-main main-font" width="100" height="30" />
    </bodyblock>
  </reportblock>
</report>