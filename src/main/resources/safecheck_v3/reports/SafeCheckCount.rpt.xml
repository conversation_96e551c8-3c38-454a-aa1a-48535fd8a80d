<report>
  <sql name="getCheckerName" sql="getSafeCheckerName" param="{condition:condition,plan_date_start:plan_date_start,plan_date_end:plan_date_end,f_filialeid:f_filialeid}" />
  <sql name="getDefectLevel" sql="getSafeDefectLevel" param="{condition:condition,plan_date_start:plan_date_start,plan_date_end:plan_date_end,f_filialeid:f_filialeid}" />
  <sql name="getDeviceType" sql="getSafeDeviceType" param="{condition:condition,plan_date_start:plan_date_start,plan_date_end:plan_date_end,f_filialeid:f_filialeid}" />
  <sql name="getItemName" sql="getSafeItemName" param="{condition:condition,plan_date_start:plan_date_start,plan_date_end:plan_date_end,f_filialeid:f_filialeid}" />
  <column number="0" width="100" startx="0" />
  <column number="1" width="100" startx="100" />
  <column number="2" width="100" startx="200" />
  <column number="3" width="100" startx="300" />
  <column number="4" width="80" startx="400" />
  <column number="5" width="80" startx="480" />
  <column number="6" width="80" startx="560" />
  <column number="7" width="80" startx="640" />
  <column number="8" width="80" startx="720" />
  <column number="9" width="80" startx="800" />
  <column number="10" width="80" startx="880" />
  <row number="0" height="30" starty="0" />
  <row number="1" height="30" starty="30" />
  <row number="2" height="30" starty="60" />
  <row number="3" height="30" starty="90" />
  <row number="4" height="30" starty="120" />
  <row number="5" height="30" starty="150" />
  <row number="6" height="5" starty="180" />
  <reportblock row="0" column="0" rowspan="6" columnspan="11" content="" css="" width="960" height="180">
    <headblock row="0" column="0" rowspan="2" columnspan="11" content="" css="" width="960" height="60" name="">
      <cell row="0" column="0" rowspan="2" columnspan="1" content="$统计类型$" css="tdcenter report-primary" width="100" height="60" />
      <cell row="0" column="1" rowspan="2" columnspan="1" content="$隐患类型$" css="tdcenter report-primary" width="100" height="60" />
      <cell row="0" column="2" rowspan="2" columnspan="2" content="$隐患项目$" css="tdcenter report-primary" width="200" height="60" />
      <cell row="0" column="4" rowspan="1" columnspan="3" content="$本年隐患整改$" css="tdcenter report-primary" width="240" height="30" />
      <cell row="0" column="7" rowspan="1" columnspan="3" content="$往年隐患整改$" css="tdcenter report-primary" width="240" height="30" />
      <cell row="0" column="10" rowspan="2" columnspan="1" content="$隐患整改率$" css="tdcenter report-primary" width="80" height="60" />
      <cell row="1" column="4" rowspan="1" columnspan="1" content="$隐患数量$" css="tdcenter report-primary" width="80" height="30" />
      <cell row="1" column="5" rowspan="1" columnspan="1" content="$已处理隐患数$" css="tdcenter report-primary" width="80" height="30" />
      <cell row="1" column="6" rowspan="1" columnspan="1" content="$未处理隐患数$" css="tdcenter report-primary" width="80" height="30" />
      <cell row="1" column="7" rowspan="1" columnspan="1" content="$隐患数量$" css="tdcenter report-primary" width="80" height="30" />
      <cell row="1" column="8" rowspan="1" columnspan="1" content="$已处理隐患数$" css="tdcenter report-primary" width="80" height="30" />
      <cell row="1" column="9" rowspan="1" columnspan="1" content="$未处理隐患数$" css="tdcenter report-primary" width="80" height="30" />
    </headblock>
    <leftblock row="2" column="0" rowspan="4" columnspan="4" content="" css="" width="400" height="120" name="">
      <leftblock row="2" column="0" rowspan="3" columnspan="4" content="getCheckerName" css="" width="400" height="90" name="left1">
        <head row="2" column="0" rowspan="3" columnspan="1" content="left1.f_checker_name" css="tdcenter report-primary" width="100" height="90" />
        <leftblock row="2" column="1" rowspan="2" columnspan="3" content="getDefectLevel.where(row.f_checker_name==left1.f_checker_name)" css="" width="300" height="60" name="left2">
          <head row="2" column="1" rowspan="2" columnspan="1" content="left2.f_defect_level" css="tdcenter report-primary" width="100" height="60" />
          <leftblock row="2" column="2" rowspan="1" columnspan="2" content="getDeviceType.where(row.f_checker_name==left1.f_checker_name).where(row.f_defect_level==left2.f_defect_level)" css="" width="200" height="30" name="left3">
            <head row="2" column="2" rowspan="1" columnspan="1" content="left3.f_device_type" css="tdcenter report-primary" width="100" height="30" />
            <leftblock row="2" column="3" rowspan="1" columnspan="1" content="getItemName.where(row.f_checker_name==left1.f_checker_name).where(row.f_defect_level==left2.f_defect_level).where(row.f_device_type==left3.f_device_type)" css="" width="100" height="30" name="left4">
              <head row="2" column="3" rowspan="1" columnspan="1" content="left4.f_item_name" css="tdcenter report-primary" width="100" height="30" />
            </leftblock>
          </leftblock>
          <cell row="3" column="2" rowspan="1" columnspan="2" content="$小计$" css="tdcenter report-primary" width="200" height="30" />
        </leftblock>
        <cell row="4" column="1" rowspan="1" columnspan="3" content="$合计$" css="tdcenter report-primary" width="300" height="30" />
      </leftblock>
      <cell row="5" column="0" rowspan="1" columnspan="4" content="$总计$" css="tdcenter report-primary" width="400" height="30" />
    </leftblock>
    <bodyblock row="2" column="4" rowspan="4" columnspan="7" content="" css="" width="560" height="120" headexpression="" leftexpression="" headname="" leftname="">
      <bodyblock row="2" column="4" rowspan="3" columnspan="7" content="" css="" width="560" height="90" headexpression="" leftexpression="getCheckerName" headname="" leftname="left1">
        <bodyblock row="2" column="4" rowspan="2" columnspan="7" content="" css="" width="560" height="60" headexpression="" leftexpression="getDefectLevel.where(row.f_checker_name==left1.f_checker_name)" headname="" leftname="left2">
          <bodyblock row="2" column="4" rowspan="1" columnspan="7" content="" css="" width="560" height="30" headexpression="" leftexpression="getDeviceType.where(row.f_checker_name==left1.f_checker_name).where(row.f_defect_level==left2.f_defect_level)" headname="" leftname="left3">
            <bodyblock row="2" column="4" rowspan="1" columnspan="4" content="" css="" width="320" height="30" headexpression="" leftexpression="getItemName.where(row.f_checker_name==left1.f_checker_name).where(row.f_defect_level==left2.f_defect_level).where(row.f_device_type==left3.f_device_type)" headname="" leftname="left4">
              <cell row="2" column="4" rowspan="1" columnspan="1" content="left4.t_defect_count" css="tdcenter" width="80" height="30" />
              <cell row="2" column="5" rowspan="1" columnspan="1" content="left4.t_defect_processed" css="tdcenter" width="80" height="30" />
              <cell row="2" column="6" rowspan="1" columnspan="1" content="left4.t_defect_untreated" css="tdcenter" width="80" height="30" />
              <cell row="2" column="7" rowspan="1" columnspan="1" content="left4.y_defect_count" css="tdcenter" width="80" height="30" />
            </bodyblock>
            <bodyblock row="2" column="8" rowspan="1" columnspan="3" content="" css="" width="240" height="30" headexpression="" leftexpression="getItemName.where(row.f_checker_name==left1.f_checker_name).where(row.f_defect_level==left2.f_defect_level).where(row.f_device_type==left3.f_device_type)" headname="" leftname="left4">
              <cell row="2" column="8" rowspan="1" columnspan="1" content="left4.y_defect_processed" css="tdcenter" width="80" height="30" />
              <cell row="2" column="9" rowspan="1" columnspan="1" content="left4.y_defect_untreated" css="tdcenter" width="80" height="30" />
              <cell row="2" column="10" rowspan="1" columnspan="1" content="${left4.t_rectification_rate}%$ " css="tdcenter" width="80" height="30" />
            </bodyblock>
          </bodyblock>
          <cell row="3" column="4" rowspan="1" columnspan="1" content="getItemName.where(row.f_checker_name==left1.f_checker_name).where(row.f_defect_level==left2.f_defect_level).sum(row.t_defect_count)" css="tdcenter" width="80" height="30" />
          <cell row="3" column="5" rowspan="1" columnspan="1" content="getItemName.where(row.f_checker_name==left1.f_checker_name).where(row.f_defect_level==left2.f_defect_level).sum(row.t_defect_processed)" css="tdcenter" width="80" height="30" />
          <cell row="3" column="6" rowspan="1" columnspan="1" content="getItemName.where(row.f_checker_name==left1.f_checker_name).where(row.f_defect_level==left2.f_defect_level).sum(row.t_defect_untreated)" css="tdcenter" width="80" height="30" />
          <cell row="3" column="7" rowspan="1" columnspan="1" content="getItemName.where(row.f_checker_name==left1.f_checker_name).where(row.f_defect_level==left2.f_defect_level).sum(row.y_defect_count)" css="tdcenter" width="80" height="30" />
          <cell row="3" column="8" rowspan="1" columnspan="1" content="getItemName.where(row.f_checker_name==left1.f_checker_name).where(row.f_defect_level==left2.f_defect_level).sum(row.y_defect_processed)" css="tdcenter" width="80" height="30" />
          <cell row="3" column="9" rowspan="1" columnspan="1" content="getItemName.where(row.f_checker_name==left1.f_checker_name).where(row.f_defect_level==left2.f_defect_level).sum(row.y_defect_untreated)" css="tdcenter" width="80" height="30" />
          <cell row="3" column="10" rowspan="1" columnspan="1" content="" css="tdcenter" width="80" height="30" />
        </bodyblock>
        <cell row="4" column="4" rowspan="1" columnspan="1" content="getItemName.where(row.f_checker_name==left1.f_checker_name).sum(row.t_defect_count)" css="tdcenter" width="80" height="30" />
        <cell row="4" column="5" rowspan="1" columnspan="1" content="getItemName.where(row.f_checker_name==left1.f_checker_name).sum(row.t_defect_processed)" css="tdcenter" width="80" height="30" />
        <cell row="4" column="6" rowspan="1" columnspan="1" content="getItemName.where(row.f_checker_name==left1.f_checker_name).sum(row.t_defect_untreated)" css="tdcenter" width="80" height="30" />
        <cell row="4" column="7" rowspan="1" columnspan="1" content="getItemName.where(row.f_checker_name==left1.f_checker_name).sum(row.y_defect_count)" css="tdcenter" width="80" height="30" />
        <cell row="4" column="8" rowspan="1" columnspan="1" content="getItemName.where(row.f_checker_name==left1.f_checker_name).sum(row.y_defect_processed)" css="tdcenter" width="80" height="30" />
        <cell row="4" column="9" rowspan="1" columnspan="1" content="getItemName.where(row.f_checker_name==left1.f_checker_name).sum(row.y_defect_untreated)" css="tdcenter" width="80" height="30" />
        <cell row="4" column="10" rowspan="1" columnspan="1" content="" css="tdcenter" width="80" height="30" />
      </bodyblock>
      <cell row="5" column="4" rowspan="1" columnspan="1" content="getItemName.sum(row.t_defect_count)" css="tdcenter" width="80" height="30" />
      <cell row="5" column="5" rowspan="1" columnspan="1" content="getItemName.sum(row.t_defect_processed)" css="tdcenter" width="80" height="30" />
      <cell row="5" column="6" rowspan="1" columnspan="1" content="getItemName.sum(row.t_defect_untreated)" css="tdcenter" width="80" height="30" />
      <cell row="5" column="7" rowspan="1" columnspan="1" content="getItemName.sum(row.y_defect_count)" css="tdcenter" width="80" height="30" />
      <cell row="5" column="8" rowspan="1" columnspan="1" content="getItemName.sum(row.y_defect_processed)" css="tdcenter" width="80" height="30" />
      <cell row="5" column="9" rowspan="1" columnspan="1" content="getItemName.sum(row.y_defect_untreated)" css="tdcenter" width="80" height="30" />
      <cell row="5" column="10" rowspan="1" columnspan="1" content="" css="tdcenter" width="80" height="30" />
    </bodyblock>
  </reportblock>
  <cell row="6" column="0" rowspan="1" columnspan="11" content="" css="" width="960" height="5" />
</report>
