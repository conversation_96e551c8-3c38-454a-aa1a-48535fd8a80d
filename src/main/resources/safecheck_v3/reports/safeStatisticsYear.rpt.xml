<report>
  <sql name="getLeftNameTwo" sql="safeStatisticsYearReportSql2" param="{condition:condition}" />
  <sql name="getLeftNameOne" sql="CheckerReportSql1" param="{condition:condition}" />
  <sql name="getLeftNameThree" sql="safeStatisticsYearReportSql3" param="{condition:condition}" />
  <column number="0" width="100" startx="0" />
  <column number="1" width="100" startx="100" />
  <column number="2" width="100" startx="200" />
  <column number="3" width="100" startx="300" />
  <column number="4" width="100" startx="400" />
  <column number="5" width="100" startx="500" />
  <column number="6" width="100" startx="600" />
  <column number="7" width="100" startx="700" />
  <column number="8" width="100" startx="800" />
  <column number="9" width="100" startx="900" />
  <column number="10" width="100" startx="1000" />
  <column number="11" width="100" startx="1100" />
  <column number="12" width="100" startx="1200" />
  <column number="13" width="100" startx="1300" />
  <row number="0" height="30" starty="0" />
  <row number="1" height="30" starty="30" />
  <row number="2" height="30" starty="60" />
  <row number="3" height="30" starty="90" />
  <row number="4" height="30" starty="120" />
  <reportblock row="0" column="0" rowspan="5" columnspan="14" content="" css="" width="1400" height="150">
    <headblock row="0" column="0" rowspan="2" columnspan="14" content="" css="" width="1400" height="60" name="">
      <cell row="0" column="0" rowspan="2" columnspan="1" content="$小区名称$" css="tdcenter report-primary font-size19" width="100" height="60" />
      <cell row="0" column="1" rowspan="2" columnspan="1" content="$年份$" css="tdcenter report-primary font-size19" width="100" height="60" />
      <cell row="0" column="2" rowspan="2" columnspan="1" content="$计划安检总户数$" css="tdcenter report-primary font-size19" width="100" height="60" />
      <cell row="0" column="3" rowspan="2" columnspan="1" content="$未检户数$" css="tdcenter report-primary font-size19" width="100" height="60" />
      <cell row="0" column="4" rowspan="2" columnspan="1" content="$已检户数$" css="tdcenter report-primary font-size19" width="100" height="60" />
      <cell row="0" column="5" rowspan="2" columnspan="1" content="$入户户数$" css="tdcenter report-primary font-size19" width="100" height="60" />
      <cell row="0" column="6" rowspan="1" columnspan="4" content="$到访不遇户数$" css="tdcenter report-primary font-size19" width="400" height="30" />
      <cell row="0" column="10" rowspan="2" columnspan="1" content="$拒检户数$" css="tdcenter report-primary font-size19" width="100" height="60" />
      <cell row="0" column="11" rowspan="2" columnspan="1" content="$安检率$" css="tdcenter report-primary font-size19" width="100" height="60" />
      <cell row="0" column="12" rowspan="2" columnspan="1" content="$申请安检户数$" css="tdcenter report-primary font-size19" width="100" height="60" />
      <cell row="0" column="13" rowspan="2" columnspan="1" content="$总入户数$" css="tdcenter report-primary font-size19" width="100" height="60" />
      <cell row="1" column="6" rowspan="1" columnspan="1" content="$1次$" css="tdcenter report-primary font-size19" width="100" height="30" />
      <cell row="1" column="7" rowspan="1" columnspan="1" content="$2次$" css="tdcenter report-primary font-size19" width="100" height="30" />
      <cell row="1" column="8" rowspan="1" columnspan="1" content="$3次$" css="tdcenter report-primary font-size19" width="100" height="30" />
      <cell row="1" column="9" rowspan="1" columnspan="1" content="$小计$" css="tdcenter report-primary font-size19" width="100" height="30" />
    </headblock>
    <leftblock row="2" column="0" rowspan="3" columnspan="2" content="" css="" width="200" height="90" name="">
      <leftblock row="2" column="0" rowspan="2" columnspan="2" content="getLeftNameOne" css="" width="200" height="60" name="left1">
        <head row="2" column="0" rowspan="2" columnspan="1" content="left1.f_residential_area" css="tdcenter report-primary font-size19" width="100" height="60" />
        <leftblock row="2" column="1" rowspan="1" columnspan="1" content="getLeftNameTwo.where(row.f_residential_area==left1.f_residential_area)" css="" width="100" height="30" name="left2">
          <head row="2" column="1" rowspan="1" columnspan="1" content="left2.f_plan_year" css="tdcenter report-primary font-size19" width="100" height="30" />
        </leftblock>
        <cell row="3" column="1" rowspan="1" columnspan="1" content="$小计$" css="tdcenter report-primary font-size19" width="100" height="30" />
      </leftblock>
      <leftblock row="4" column="0" rowspan="1" columnspan="2" content="getLeftNameThree" css="" width="200" height="30" name="left3">
        <head row="4" column="0" rowspan="1" columnspan="2" content="left3.f_plan_year" css="tdcenter report-primary font-size19" width="200" height="30" />
      </leftblock>
    </leftblock>
    <bodyblock row="2" column="2" rowspan="3" columnspan="12" content="" css="" width="1200" height="90" headexpression="" leftexpression="" headname="" leftname="">
      <bodyblock row="2" column="2" rowspan="2" columnspan="12" content="" css="" width="1200" height="60" headexpression="" leftexpression="getLeftNameOne" headname="" leftname="left1">
        <bodyblock row="2" column="2" rowspan="1" columnspan="12" content="" css="" width="1200" height="30" headexpression="" leftexpression="getLeftNameTwo.where(row.f_residential_area==left1.f_residential_area)" headname="" leftname="left2">
          <cell row="2" column="2" rowspan="1" columnspan="1" content="left2.data1" css="tdcenter report-success" width="100" height="30" />
          <cell row="2" column="3" rowspan="1" columnspan="1" content="left2.data2" css="tdcenter report-success" width="100" height="30" />
          <cell row="2" column="4" rowspan="1" columnspan="1" content="left2.data3" css="tdcenter report-success" width="100" height="30" />
          <cell row="2" column="5" rowspan="1" columnspan="1" content="left2.data4" css="tdcenter report-success" width="100" height="30" />
          <cell row="2" column="6" rowspan="1" columnspan="1" content="left2.data5" css="tdcenter report-success" width="100" height="30" />
          <cell row="2" column="7" rowspan="1" columnspan="1" content="left2.data6" css="tdcenter report-success" width="100" height="30" />
          <cell row="2" column="8" rowspan="1" columnspan="1" content="left2.data7" css="tdcenter report-success" width="100" height="30" />
          <cell row="2" column="9" rowspan="1" columnspan="1" content="left2.data8" css="tdcenter report-success" width="100" height="30" />
          <cell row="2" column="10" rowspan="1" columnspan="1" content="left2.data9" css="tdcenter report-success" width="100" height="30" />
          <cell row="2" column="11" rowspan="1" columnspan="1" content="left2.data10" css="tdcenter report-success" width="100" height="30" />
          <cell row="2" column="12" rowspan="1" columnspan="1" content="left2.data11" css="tdcenter report-success" width="100" height="30" />
          <cell row="2" column="13" rowspan="1" columnspan="1" content="left2.data12" css="tdcenter report-success" width="100" height="30" />
        </bodyblock>
        <cell row="3" column="2" rowspan="1" columnspan="1" content="getLeftNameTwo.where(row.f_residential_area==left1.f_residential_area).sum(row.data1)" css="tdcenter report-success" width="100" height="30" />
        <cell row="3" column="3" rowspan="1" columnspan="1" content="getLeftNameTwo.where(row.f_residential_area==left1.f_residential_area).sum(row.data2)" css="tdcenter report-success" width="100" height="30" />
        <cell row="3" column="4" rowspan="1" columnspan="1" content="getLeftNameTwo.where(row.f_residential_area==left1.f_residential_area).sum(row.data3)" css="tdcenter report-success" width="100" height="30" />
        <cell row="3" column="5" rowspan="1" columnspan="1" content="getLeftNameTwo.where(row.f_residential_area==left1.f_residential_area).sum(row.data4)" css="tdcenter report-success" width="100" height="30" />
        <cell row="3" column="6" rowspan="1" columnspan="1" content="getLeftNameTwo.where(row.f_residential_area==left1.f_residential_area).sum(row.data5)" css="tdcenter report-success" width="100" height="30" />
        <cell row="3" column="7" rowspan="1" columnspan="1" content="getLeftNameTwo.where(row.f_residential_area==left1.f_residential_area).sum(row.data6)" css="tdcenter report-success" width="100" height="30" />
        <cell row="3" column="8" rowspan="1" columnspan="1" content="getLeftNameTwo.where(row.f_residential_area==left1.f_residential_area).sum(row.data7)" css="tdcenter report-success" width="100" height="30" />
        <cell row="3" column="9" rowspan="1" columnspan="1" content="getLeftNameTwo.where(row.f_residential_area==left1.f_residential_area).sum(row.data8)" css="tdcenter report-success" width="100" height="30" />
        <cell row="3" column="10" rowspan="1" columnspan="1" content="getLeftNameTwo.where(row.f_residential_area==left1.f_residential_area).sum(row.data9)" css="tdcenter report-success" width="100" height="30" />
        <cell row="3" column="11" rowspan="1" columnspan="1" content="" css="tdcenter report-success" width="100" height="30" />
        <cell row="3" column="12" rowspan="1" columnspan="1" content="getLeftNameTwo.where(row.f_residential_area==left1.f_residential_area).sum(row.data11)" css="tdcenter report-success" width="100" height="30" />
        <cell row="3" column="13" rowspan="1" columnspan="1" content="getLeftNameTwo.where(row.f_residential_area==left1.f_residential_area).sum(row.data12)" css="tdcenter report-success" width="100" height="30" />
      </bodyblock>
      <bodyblock row="4" column="2" rowspan="1" columnspan="12" content="" css="" width="1200" height="30" headexpression="" leftexpression="getLeftNameThree" headname="" leftname="left3">
        <cell row="4" column="2" rowspan="1" columnspan="1" content="left3.data1" css="tdcenter report-success" width="100" height="30" />
        <cell row="4" column="3" rowspan="1" columnspan="1" content="left3.data2" css="tdcenter report-success" width="100" height="30" />
        <cell row="4" column="4" rowspan="1" columnspan="1" content="left3.data3" css="tdcenter report-success" width="100" height="30" />
        <cell row="4" column="5" rowspan="1" columnspan="1" content="left3.data4" css="tdcenter report-success" width="100" height="30" />
        <cell row="4" column="6" rowspan="1" columnspan="1" content="left3.data5" css="tdcenter report-success" width="100" height="30" />
        <cell row="4" column="7" rowspan="1" columnspan="1" content="left3.data6" css="tdcenter report-success" width="100" height="30" />
        <cell row="4" column="8" rowspan="1" columnspan="1" content="left3.data7" css="tdcenter report-success" width="100" height="30" />
        <cell row="4" column="9" rowspan="1" columnspan="1" content="left3.data8" css="tdcenter report-success" width="100" height="30" />
        <cell row="4" column="10" rowspan="1" columnspan="1" content="left3.data9" css="tdcenter report-success" width="100" height="30" />
        <cell row="4" column="11" rowspan="1" columnspan="1" content="$$" css="tdcenter report-success" width="100" height="30" />
        <cell row="4" column="12" rowspan="1" columnspan="1" content="left3.data11" css="tdcenter report-success" width="100" height="30" />
        <cell row="4" column="13" rowspan="1" columnspan="1" content="left3.data12" css="tdcenter report-success" width="100" height="30" />
      </bodyblock>
    </bodyblock>
  </reportblock>
</report>
