<?xml version="1.0"  encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
    <!--安检单-->
    <class entity-name="t_check_paper_del" table="t_check_paper_del">
        <id name="id" type="string">
            <generator class="uuid.hex"/>
        </id>
        <!-- 旧id -->
        <property name="f_old_id" type="string"/>
        <!-- 安检项 id   一对多外键 -->
        <property name="f_check_item_id" type="string"/>
        <!-- 安检计划 id -->
        <property name="f_check_plan_id" type="string"/>
        <!-- 安检人id -->
        <property name="f_checker_id" type="string"/>
        <!-- 安检是否完成 -->
        <property name="f_complete" type="string"/>
        <!-- 用户编号code -->
        <property name="f_userinfo_code" type="string"/>
        <!-- 预约日期 -->
        <property name="f_prearranged_date" type="string"/>
        <!-- 有无计划安检 -->
        <property name="f_no_checkplan" type="string"/>
        <!-- 安检人名称 -->
        <property name="f_checker_name" type="string"/>
        <!-- 安检基本信息 -->
        <!-- 入户时间 YYYY-MM-dd HH:mm:ss-->
        <property name="f_onsite_time" type="string"/>
        <!-- 离开时间 YYYY-MM-dd HH:mm:ss-->
        <property name="f_offsite_time" type="string"/>
        <!-- 入户状态 未用天然气、到访不遇、拒绝入户、入户-->
        <property name="f_entry_status" type="string"/>
        <!--是否使用天然气 已使用/未使用-->
        <property name="f_usegas" type="string"/>
        <!-- 安检状态 未检 已检  -->
        <property name="f_state" type="string"/>
        <!-- 上传状态 -->
        <property name="f_upload_state" type="string"/>
        <!-- 未使用天然气 照片-->
        <property name="f_nongasuser_path" type="string"/>
        <!-- 到访不遇 照片-->
        <property name="f_noanswer_path" type="string"/>
        <!-- 拒检 照片-->
        <property name="f_rejectcheck_path" type="string"/>
        <!-- 无燃气具 -->
        <property name="f_no_gas_device" type="string"/>
        <!-- 安检类型 民用安检、非民用安检-->
        <property name="f_check_type" type="string"/>
        <!-- 非民用用户类型 公福、工业、餐饮、锅炉 -->
        <property name="f_noncivil_style" type="string"/>
        <!-- 暂停使用天然气 -->
        <property name="f_stop_service" type="string"/>
        <!-- 本期用气量 -->
        <property name="f_usage" type="int"/>
        <!-- 签名图片名称  -->
        <property name="f_signname" type="string"/>
        <!-- 签名图片id  -->
        <property name="f_sign_imgid" type="string"/>
        <!-- 签名图片安卓端全路径 -->
        <property name="f_sign_path" type="string"/>
        <!-- 用户是否阅读须知内容 -->
        <property name="f_read_instructions" type="string"/>
        <!-- 用户是否被加入黑名单 -->
        <property name="f_blacklist" type="string"/>
        <!-- 拉黑原因 -->
        <property name="f_blocking_cause" type="string"/>

        <!-- 用户基本信息 -->
        <!-- 所属子公司 -->
        <property name="f_subcompany" type="string"/>
        <!-- 用户名-->
        <property name="f_user_name" type="string"/>
        <!-- 用户电话-->
        <property name="f_user_phone" type="string"/>
        <!-- 用户ID-->
        <property name="f_userinfoid" type="string"/>
        <!-- 卡号 -->
        <property name="f_card_id" type="string"/>
        <!-- 六级地址  -->
        <!--区 -->
        <property name="f_area" type="string"/>
        <!-- 街道 -->
        <property name="f_street" type="string"/>
        <!-- 小区 -->
        <property name="f_residential_area" type="string"/>
        <!-- 楼号 -->
        <property name="f_building" type="string"/>
        <!-- 单元-->
        <property name="f_unit" type="string"/>
        <!-- 楼层 -->
        <property name="f_floor" type="string"/>
        <!-- 门牌号-->
        <property name="f_room" type="string"/>
        <!-- 详细地址-->
        <property name="f_address" type="string"/>
        <!-- 房屋结构 -->
        <property name="f_room_style" type="string"/>
        <!-- 供暖方式 -->
        <property name="f_heating_style" type="string"/>
        <!-- 其他供暖方式 -->
        <property name="f_heating_style_" type="string"/>

        <!-- 录音和照片 -->
        <!-- 录音文件位置 -->
        <property name="f_recording1_path" type="string"/>
        <!-- 录音文件位置 -->
        <property name="f_recording2_path" type="string"/>
        <!-- 录音文件位置 -->
        <property name="f_recording3_path" type="string"/>
        <!-- 照片位置 -->
        <property name="f_pic1_path" type="string"/>
        <!-- 照片位置 -->
        <property name="f_pic2_path" type="string"/>
        <!-- 照片位置 -->
        <property name="f_pic3_path" type="string"/>

        <!-- 操作人员居住-->
        <property name="f_defect_dorm" type="string"/>
        <!-- 扩容-->
        <property name="f_defect_expand" type="string"/>
        <!-- 其他-->
        <property name="f_defect_other" type="string"/>
        <!-- 不固定-->
        <property name="f_defect_notfixed" type="string"/>
        <!-- 不固定照片-->
        <property name="f_notfixed_path" type="string"/>
        <!-- 安检最终处置 关闭表前阀、取下表内电池-->
        <!-- 关闭表前阀 -->
        <property name="f_close_valve" type="string"/>
        <!-- 关闭表前阀 照片-->
        <property name="f_close_valve_path" type="string"/>
        <!-- 取下表内电池-->
        <property name="f_unload_battery" type="string"/>
        <!-- 整改策略 暂停燃气器具-->
        <property name="f_stop_devices" type="string"/>
        <!-- 整改策略 表内气量清零-->
        <property name="f_zero_meter" type="string"/>
        <!-- 整改策略 切断气源-->
        <property name="f_cutoff_gas" type="string"/>
        <!-- 切断气源照片-->
        <property name="f_cutoff_gas_path" type="string"/>
        <!-- 整改策略 管道放空-->
        <property name="f_empty_pipe" type="string"/>
        <!-- 整改策略 关闭调压器-->
        <property name="f_close_adjuster" type="string"/>
        <!-- 整改策略 关闭调压器照片-->
        <property name="f_close_adjuster_path" type="string"/>
        <!-- 隐患通知书 是否 -->
        <property name="f_defect_notified" type="string"/>
        <!-- 隐患通知书 照片-->
        <property name="f_notified_path" type="string"/>
        <!-- 隐患登记 是否-->
        <property name="f_defect_registered" type="string"/>
        <!-- 蓉城到访不遇后是否进行激光检漏 是否-->
        <property name="f_laser_check" type="string"/>
        <!-- 满意度 -->
        <!-- 安检整体照片 -->
        <!--<property name="f_overall_path" type="string" />-->
        <set name="f_overall_imgs" cascade="delete">
            <key column="f_paper_id" on-delete="noaction"/>
            <one-to-many entity-name="t_paper_overall_imgs_del" not-found="exception"/>
        </set>
        <!-- 安检整体照片2 -->
        <property name="f_overall2_path" type="string"/>
        <!-- 客户建议 -->
        <property name="f_client_suggestion" type="string"/>
        <!-- 客户满意度 -->
        <property name="f_client_evaluation" type="string"/>
        <!-- 维修人 -->
        <property name="f_repairman" type="string"/>
        <!-- 维修级别 -->
        <property name="f_defect_urgency" type="string"/>
        <!-- 维修结果（蓉城该字段用来区分是否成功调用维修接口 未派维修单/已派维修单）-->
        <property name="f_repaired" type="string"/>
        <!-- 维修结果描述 -->
        <property name="f_ratification_des" type="string"/>
        <!-- 维修结果-->
        <property name="f_repaired_uploaded" type="string"/>
        <!-- 维修上传日期 -->
        <property name="f_repair_date" type="string"/>
        <!-- 维修结果录音文件位置 -->
        <property name="f_amr_path" type="string"/>
        <!-- 经度 -->
        <property name="f_longitude" type="string"/>
        <!-- 纬度 -->
        <property name="f_latitude" type="string"/>
        <!-- 备注    -->
        <property name="f_remark" type="string"/>
        <!-- 审核标记 -->
        <property name="f_approved" type="string"/>
        <!-- 审核时间 -->
        <property name="f_approved_time" type="string"/>
        <!-- 审核人 -->
        <property name="f_approved_by" type="string"/>
        <!-- 审核说明 -->
        <property name="f_approved_note" type="string"/>
        <!-- 审核标记 -->
        <property name="f_repair_approved" type="string"/>
        <!-- 审核时间 -->
        <property name="f_repair_approved_time" type="string"/>
        <!-- 审核人 -->
        <property name="f_repair_approved_by" type="string"/>
        <!-- 审核说明 -->
        <property name="f_repair_approved_note" type="string"/>
        <!-- 对应维修f_service_id (蓉城此字段用来保存维修工单号)-->
        <property name="f_SafeToRepair_id" type="string"/>
        <!--安检结果-->
        <property name="f_defect_content" type="string" length="4096"/>
        <!-- 组织机构 -->
        <property name="f_orgstr" type="string" length="200"/>
        <!--公司机构ids -->
        <property name="f_filialeids" type="string"/>
        <!--所属公司 -->
        <property name="f_filialeid" type="string"/>

        <!-- 身份证号码 -->
        <property name="f_idnumber" type="string"/>
        <!-- 冗余关联计划列 -->
        <property name="f_plan_id" type="string"/>
        <!-- 维修单id -->
        <!--<property name="f_repairorder_id" type="int" />-->
        <!-- 备注信息 -->
        <property name="f_comments" type="string" length="255"/>
        <!--用户性质 -->
        <property name="f_userproperties" type="string" length="50"/>
        <!-- 安检单上传时间 -->
        <property name="f_upload_date" type="string"/>
        <!-- 标记是否发送短信 -->
        <property name="f_send_message" type="string"/>

        <property name="f_user_inhome_time" type="string" length="255"/>
        <property name="f_user_check_time" type="string" length="255"/>
        <property name="f_user_spare_phone" type="string" length="255"/>
        <property name="f_user_family_remarks" type="string" length="255"/>
        <property name="f_user_equipment_remarks" type="string" length="255"/>
        <!-- 购气类型(按金额，按气量) -->
        <property name="f_collection_type" type="string" length="255"/>

        <!--送气单的超期原因-->
        <property name="f_exceed_reason" type="string" length="255"/>
        <property name="f_danger_state" type="string" length="255"/>
        <!-- 安检类型-->
        <property name="f_safecheck_type" type="string"/>
        <!-- 补录标志，null为正常 -->
        <property name="f_supplement_tag" type="string"/>
        <!-- 是否抄表 已抄表/未抄表/null -->
        <property name="f_is_meterreadding" type="string"/>
        <property name="f_ht_date" type="string"/>
        <!-- 到访不遇 照片-->
        <property name="f_noanswer_path1" type="string"/>
        <!-- 到访不遇 照片-->
        <property name="f_noanswer_path2" type="string"/>
        <!-- 到访不遇 照片-->
        <property name="f_noanswer_path3" type="string"/>

        <!-- 是否无人户 是/否-->
        <property name="f_safe_nohome" type="string"/>
        <!-- 是否壁挂炉用户 是/否-->
        <property name="f_safe_hasfurnace" type="string"/>
        <!-- 是否灶具用户 是/否-->
        <property name="f_safe_haskitchen" type="string"/>
        <!-- 安检项 -->
        <!-- 安检单来源-->
        <property name="f_source" type="string"/>
        <!-- 保险到期日期-->
        <property name="f_insurance_end_date" type="string"/>
        <!-- 上次抄表日期-->
        <property name="f_hand_date" type="string"/>

        <!-- 安检项 -->
        <set name="f_devices" cascade="delete">
            <key column="f_paper_id" on-delete="noaction"/>
            <one-to-many entity-name="t_paper_devices_del" not-found="exception"/>
        </set>
    </class>
</hibernate-mapping>
