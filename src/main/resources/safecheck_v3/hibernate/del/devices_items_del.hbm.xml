<?xml version="1.0"  encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
    <!--计划项-->
    <class entity-name="t_devices_items_del" table="t_devices_items_del">
        <id name="id" type="string">
            <generator class="uuid.hex"/>
        </id>
        <!-- 旧id -->
        <property name="f_old_id" type="string"/>
        <!--检查项信息名称 -->
        <property name="f_item_name" type="string"/>
        <!--检查项信息值类型-->
        <property name="type" type="string"/>
        <!--检查项信息值 -->
        <property name="f_item_value" type="string"/>
        <!-- 隐患级别-->
        <property name="f_is_defect" type="string"/>
        <!-- 原照片  -->
        <property name="f_path" type="string"/>
        <!-- 原照片1  -->
        <property name="f_p1_path" type="string"/>
        <!-- 原照片2  -->
        <property name="f_p2_path" type="string"/>
        <!-- 原照片3  -->
        <property name="f_p3_path" type="string"/>
        <property name="f_p4_path" type="string"/>
        <property name="f_p5_path" type="string"/>
        <property name="f_p6_path" type="string"/>
        <!-- 隐患级别-->
        <property name="f_defect_level" type="string"/>
        <!-- 是否进行现场处理-->
        <property name="f_live_dispose" type="string"/>
        <!-- 是否已处理-->
        <property name="f_is_repaired" type="string"/>
        <!-- 维修后图片 -->
        <property name="f_repair_path" type="string"/>
        <!-- 维修人 -->
        <property name="f_repairman" type="string"/>
        <!-- 维修时间 -->
        <property name="f_repair_date" type="string"/>
        <!-- 关联 -->
        <property name="f_device_id" type="int"/>
        <!-- 是否用户整改-->
        <property name="f_user_changes" type="string"/>
        <!-- 安检项信息 -->
        <set name="f_item_lists" cascade="delete">
            <key column="f_item_id" on-delete="noaction"/>
            <one-to-many entity-name="t_devices_items_lists_del" not-found="exception"/>
        </set>
    </class>
</hibernate-mapping>
