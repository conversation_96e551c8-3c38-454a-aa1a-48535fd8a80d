<?xml version="1.0"  encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
    <!--计划项-->
    <class entity-name="t_devices_items_lists_del" table="t_devices_items_lists_del">
        <id name="id" type="int">
            <generator class="native"></generator>
        </id>
        <!-- 旧id -->
        <property name="f_old_id" type="int"/>
        <!--检查项信息值 -->
        <property name="f_item_value" type="string"/>
        <!-- 是否为隐患 -->
        <property name="f_is_defect" type="string"/>
        <!-- 隐患级别-->
        <property name="f_defect_level" type="string"/>
        <!-- 是否进行现场处理-->
        <property name="f_live_dispose" type="string"/>
        <!-- 关联 -->
        <property name="f_item_id" type="string"/>
        <!-- 是否用户整改-->
        <property name="f_user_changes" type="string"/>
        <!-- 一级隐患是否消除-->
        <property name="f_is_eliminate" type="string"/>
        <!-- 创建时间 -->
        <property name="f_create_time" type="string"/>
        <!-- 是否整改 -->
        <property name="f_dealwith" type="string"/>
        <!-- 整改图片 -->
        <property name="f_dealpath" type="string"/>
        <!-- 整改人 -->
        <property name="f_dealer" type="string"/>
        <!-- 整改人id -->
        <property name="f_dealer_id" type="string"/>
        <!-- 整改备注 -->
        <property name="f_deal_remark" type="string"/>
    </class>
</hibernate-mapping>
