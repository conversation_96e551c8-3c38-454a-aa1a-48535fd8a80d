<?xml version="1.0"  encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
    <!--安检单对应的计数信息-->
    <class entity-name="t_paper_devices_del" table="t_paper_devices_del">
        <id name="id" type="int">
            <generator class="native"></generator>
        </id>
        <!-- 旧id -->
        <property name="f_old_id" type="int"/>
        <!-- 设备类型-->
        <property name="f_device_type" type="string"/>
        <!-- 关联-->
        <property name="f_paper_id" type="string"/>
        <!-- 安检项信息 -->
        <set name="f_items" cascade="delete">
            <key column="f_device_id" on-delete="noaction"/>
            <one-to-many entity-name="t_devices_items_del" not-found="exception"/>
        </set>
    </class>
</hibernate-mapping>
