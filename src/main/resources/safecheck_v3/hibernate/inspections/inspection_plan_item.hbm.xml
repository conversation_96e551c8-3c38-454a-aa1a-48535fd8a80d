<?xml version="1.0"  encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
    <!--巡检计划项-->
    <class entity-name="t_inspection_plan_item" table="t_inspection_plan_item">
        <id name="id" type="string">
            <generator class="assigned"/>
        </id>
        <!-- 所属子公司 -->
        <property name="f_subcompany" type="string"/>
        <!-- 用户类型 商业 居民-->
        <property name="f_user_type" type="string"/>
        <!-- 档案id -->
        <property name="f_userinfoid" type="string"/>
        <!-- 用户编号code -->
        <property name="f_userinfo_code" type="string"/>
        <!-- 用户地址id -->
        <property name="f_addressid" type="string"/>
        <!-- 用户名 -->
        <property name="f_user_name" type="string"/>
        <!-- 用户电话 -->
        <property name="f_user_phone" type="string"/>
        <!-- 新用户电话 -->
        <property name="f_user_phone2" type="string"/>
        <!--用户建档日期 -->
        <property name="f_newfile_date" type="timestamp"/>
        <!-- 状态  已检、未检、重检-->
        <property name="f_state" type="string"/>
        <!-- 巡检是否完成 -->
        <property name="f_complete" type="string"/>
        <!-- 街道 -->
        <property name="f_slice_area" type="string"/>
        <!-- 是否使用天然氣 -->
        <property name="f_usegas" type="string"/>
        <!--区 -->
        <property name="f_area" type="string"/>
        <!-- 街道 -->
        <property name="f_street" type="string"/>
        <!-- 小区 -->
        <property name="f_residential_area" type="string"/>
        <!-- 楼号 -->
        <property name="f_building" type="string"/>
        <!-- 单元-->
        <property name="f_unit" type="string"/>
        <!-- 楼层 -->
        <property name="f_floor" type="string"/>
        <!-- 门牌号-->
        <property name="f_room" type="string"/>
        <!-- 详细地址-->
        <property name="f_address" type="string"/>
        <!-- 有无计划巡检 -->
        <property name="f_no_checkplan" type="string"/>
        <!-- 备注 -->
        <property name="f_remark" type="string"/>
        <!-- 冗余关联列 -->
        <property name="f_plan_id" type="string"/>
        <!-- 上传状态 -->
        <property name="f_upload_state" type="string"/>
        <!-- 审核标记 -->
        <property name="f_approved" type="string"/>
        <!-- 审核时间 -->
        <property name="f_approved_time" type="string"/>
        <!-- 审核人 -->
        <property name="f_approved_by" type="string"/>
        <!-- 审核说明 -->
        <property name="f_approved_note" type="string"/>
        <!-- 审核标记 -->
        <property name="f_repair_approved" type="string"/>
        <!-- 审核时间 -->
        <property name="f_repair_approved_time" type="string"/>
        <!-- 审核人 -->
        <property name="f_repair_approved_by" type="string"/>
        <!-- 审核说明 -->
        <property name="f_repair_approved_note" type="string"/>
        <!-- 是否通知巡检员 -->
        <property name="f_approval_notified" type="string"/>
        <!-- 上次巡检状态 -->
        <property name="f_last_check_state" type="string"/>
        <!-- 上次巡检日期  -->
        <property name="f_last_check_date" type="string"/>
        <!-- 上次巡检结果  -->
        <property name="f_last_check_result" type="string" length="2000"/>
        <!--用户性质 -->
        <property name="f_userproperties" type="string" length="50"/>
        <!-- 预约日期 -->
        <property name="f_prearranged_date" type="string"/>
        <!--公司机构ids -->
        <property name="f_filialeids" type="string" length="200"/>
        <!--所属公司 -->
        <property name="f_filialeid" type="string"/>
        <!-- 身份证号码 -->
        <property name="f_idnumber" type="string"/>
        <!-- 定时提取标记,0/1,1代表已提取，0代表未提取-->
        <property name="f_flag" type="int"/>
        <!-- 生成时间 -->
        <property name="f_create_time" type="string"/>
        <!-- 版本 -->
        <property name="version" type="int"/>
        <!-- 生成的批号 -->
        <property name="f_batch_number" type="string"/>
        <!-- 巡检类型-->
        <property name="f_safecheck_type" type="string"/>
        <!-- 标记是否发送短信 -->
        <property name="f_send_message" type="string"/>

        <!--本次巡检时间-->
        <property name="f_current_check_date" type="string" length="255"/>
        <!--本次巡检状态-->
        <property name="f_current_check_state" type="string" length="255"/>
    </class>
</hibernate-mapping>
