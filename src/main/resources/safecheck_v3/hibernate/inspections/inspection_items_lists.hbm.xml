<?xml version="1.0"  encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
    <!--计划项-->
    <class entity-name="t_inspection_items_lists" table="t_inspection_items_lists">
        <id name="id" type="int">
            <generator class="native"></generator>
        </id>
        <!--检查项信息值 -->
        <property name="f_item_value" type="string"/>
        <!-- 是否为隐患 -->
        <property name="f_is_defect" type="string"/>
        <!-- 隐患级别-->
        <property name="f_defect_level" type="string"/>
        <!-- 是否进行现场处理-->
        <property name="f_live_dispose" type="string"/>
        <!-- 关联 -->
        <property name="f_item_id" type="string"/>
        <!-- 是否用户整改-->
        <property name="f_user_changes" type="string"/>
        <!-- 一级隐患是否消除-->
        <property name="f_is_eliminate" type="string"/>
        <!-- 创建时间 -->
        <property name="f_create_time" type="string"/>
    </class>
</hibernate-mapping>
