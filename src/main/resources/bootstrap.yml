# Tomcat
server:
  port: 9025

# Spring
spring:
  application:
    # 应用名称
    name: af-safecheck
  profiles:
    # 环境配置
    active: dev
  # 开启虚拟线程支持
  threads:
    virtual:
      enabled: true
  cloud:
    nacos:
      username: nacos
      password: Class123!
      discovery:
        # 服务注册地址
        server-addr: nacos-registration-center:30007
        username: nacos
        password: Class123!
        metadata:
          # 3秒心跳间隔
          preserved.heart.beat.interval: 3000
          # 9秒心跳超时
          preserved.heart.beat.timeout: 9000
          # 15秒后移除不可达实例
          preserved.ip.delete.timeout: 15000
      config:
        # 配置中心地址
        server-addr: nacos-config-server:30007
        # 命名空间ID
        namespace: ${spring.profiles.active}
        # 组名称
        group: SAFECHECK_GROUP
        # 配置文件格式
        file-extension: yml
  config:
    import:
      - nacos:application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}?group=DEFAULT_GROUP&refreshEnabled=true
      - nacos:application-module-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}?group=DEFAULT_GROUP&refreshEnabled=true
      - nacos:${spring.application.name}-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}?refreshEnabled=true
