--分公司 部门
SELECT
	toz.name f_subcompany,
	td.name dename,
	COUNT ( * ) num
FROM
	t_check_paper tp,
	t_department td,
	t_organization toz,
	t_user tu
WHERE
	tp.f_checker_id= tu.id
	AND tu.parentid= td.id
	AND td.parentid= toz.id
	{ f_subcompany !=$$:
  $
	AND toz.name= '{f_subcompany}' $,$$
	}
	{ plantype ==$临时安检$:
  $
	AND tp.f_no_checkplan= '无计划安检' $,$$
	}
	{ plantype ==$计划安检$:
  $
	AND tp.f_check_plan_id IN (
SELECT
	f_check_plan_id
FROM
	t_check_paper tp1,
	t_check_plan tcp
WHERE
	tp1.f_check_plan_id= tcp.id
	AND tp1.f_no_checkplan!= '无计划安检'
	AND tcp.f_checker IS NOT NULL
	) $,$$
	}
	{ plantype ==$集中安检$:
  $
	AND tp.f_check_plan_id IN (
SELECT
	f_check_plan_id
FROM
	t_check_paper tp1,
	t_check_plan tcp
WHERE
	tp1.f_check_plan_id= tcp.id
	AND tp1.f_no_checkplan!= '无计划安检'
	AND tcp.f_checker IS NULL
	) $,$$
	}
	{ startDate !=$$:$
	AND f_offsite_time >= '{startDate}' $,$$
	}
	{ endDate !=$$:$
	AND f_offsite_time <= '{endDate}' $,$$
	}
GROUP BY
	toz.name,
	td.name
